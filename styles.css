/* CSS Variables for Theme Support */
:root {
    /* Light Theme */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --accent-color: #007bff;
    --accent-hover: #0056b3;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 20px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] {
    /* Dark Theme */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: #404040;
    --accent-color: #4dabf7;
    --accent-hover: #339af0;
    --success-color: #51cf66;
    --warning-color: #ffd43b;
    --danger-color: #ff6b6b;
    --info-color: #22b8cf;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 4px 20px rgba(0, 0, 0, 0.4);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all 0.3s ease;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.header h1 {
    color: var(--accent-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.header h1 i {
    margin-right: 0.5rem;
}

.header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
}

.theme-toggle {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background-color: var(--border-color);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Filters Section */
.filters-section {
    background-color: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
}

.search-container {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-container i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-container input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.search-container input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.filter-tabs {
    display: flex;
    gap: 0.5rem;
}

.filter-tab {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.filter-tab.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.filter-tab:hover:not(.active) {
    background-color: var(--bg-tertiary);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

/* Table Styles */
.table-container {
    background-color: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.work-orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.work-orders-table th {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.work-orders-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.work-orders-table tbody tr {
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.work-orders-table tbody tr:hover {
    background-color: var(--bg-secondary);
}

.work-orders-table tbody tr.expanded {
    background-color: var(--bg-secondary);
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.in-progress {
    background-color: var(--info-color);
    color: white;
}

.status-badge.scheduled {
    background-color: var(--warning-color);
    color: #000;
}

.status-badge.drop-in {
    background-color: var(--success-color);
    color: white;
}

.status-badge.completed {
    background-color: var(--success-color);
    color: white;
}

.status-badge.pending {
    background-color: var(--warning-color);
    color: #000;
}

.status-badge.cancelled {
    background-color: var(--danger-color);
    color: white;
}

.status-badge.moved-to-tech {
    background-color: var(--info-color);
    color: white;
}

.status-badge.created {
    background-color: #6c757d;
    color: white;
}

/* Expandable Row */
.expandable-icon {
    cursor: pointer;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
}

.expandable-icon.expanded {
    transform: rotate(90deg);
}

.work-order-details {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Expanded Work Order Header */
.expanded-work-order-header {
    padding: 1rem 1.5rem;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.header-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* Expanded Work Order Table */
.expanded-work-order-table {
    padding: 0;
    overflow-x: auto;
}

.expanded-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    background-color: var(--bg-primary);
}

.expanded-table th {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.expanded-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    background-color: var(--bg-primary);
}

.expanded-table tbody tr:hover {
    background-color: var(--bg-secondary);
}

.expanded-table .work-order-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    margin-left: 0.5rem;
}

.expanded-table .work-order-link:hover {
    text-decoration: underline;
}

.expanded-table i.fas {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

/* Legacy detail styles for backward compatibility */
.work-order-details-content {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.detail-section h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-overlay.active {
    display: flex;
}

.modal-container {
    background-color: var(--bg-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-secondary);
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    background-color: var(--bg-secondary);
}

/* Work Order Info Bar */
.work-order-info-bar {
    background-color: var(--bg-secondary);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
    background-color: var(--bg-primary);
}

.tab-btn:hover:not(.active) {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

/* Tab Content */
.tab-content {
    padding: 2rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group input[readonly] {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Asset Details Specific Styles */
.asset-warranty-section h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.warranty-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.warranty-column h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.asset-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

/* Planning Grid */
.planning-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.planning-section h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

/* Job Details Specific Styles */
.status-badges-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: 8px;
}

.status-badges-container .status-badge {
    font-size: 0.75rem;
}

.status-badge.created {
    background-color: #6c757d;
    color: white;
}

.status-badge.moved-to-tech {
    background-color: var(--info-color);
    color: white;
}

.status-badge.job-completed {
    background-color: var(--success-color);
    color: white;
}

.status-badge.assign-to-parts {
    background-color: #fd7e14;
    color: white;
}

.status-badge.billed {
    background-color: #20c997;
    color: white;
}

.status-badge.closed {
    background-color: #6f42c1;
    color: white;
}

.job-form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-tabs {
        justify-content: center;
    }

    .action-buttons {
        justify-content: center;
    }

    .table-container {
        overflow-x: auto;
    }

    .work-orders-table {
        min-width: 800px;
    }

    .modal-container {
        margin: 0.5rem;
        max-height: 95vh;
    }

    .modal-header,
    .modal-footer {
        padding: 1rem;
    }

    .tab-content {
        padding: 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .warranty-grid,
    .planning-grid {
        grid-template-columns: 1fr;
    }

    .work-order-info-bar {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .tab-navigation {
        overflow-x: auto;
    }

    .tab-btn {
        white-space: nowrap;
        min-width: 150px;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
