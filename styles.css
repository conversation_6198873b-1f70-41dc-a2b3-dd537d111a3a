/* Google Material Design CSS Variables */
:root {
    /* Light Theme - Material Design 3 */
    --md-sys-color-primary: #1976d2;
    --md-sys-color-on-primary: #ffffff;
    --md-sys-color-primary-container: #e3f2fd;
    --md-sys-color-on-primary-container: #0d47a1;

    --md-sys-color-surface: #ffffff;
    --md-sys-color-surface-variant: #f5f5f5;
    --md-sys-color-surface-container: #fafafa;
    --md-sys-color-surface-container-high: #f0f0f0;
    --md-sys-color-on-surface: #1c1b1f;
    --md-sys-color-on-surface-variant: #49454f;

    --md-sys-color-outline: #e0e0e0;
    --md-sys-color-outline-variant: #f5f5f5;

    --md-sys-color-success: #4caf50;
    --md-sys-color-warning: #ff9800;
    --md-sys-color-error: #f44336;
    --md-sys-color-info: #2196f3;

    /* Elevation shadows */
    --md-sys-elevation-1: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24);
    --md-sys-elevation-2: 0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23);
    --md-sys-elevation-3: 0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23);
    --md-sys-elevation-4: 0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22);

    /* Typography */
    --md-sys-typescale-display-large: 57px;
    --md-sys-typescale-headline-large: 32px;
    --md-sys-typescale-headline-medium: 28px;
    --md-sys-typescale-title-large: 22px;
    --md-sys-typescale-title-medium: 16px;
    --md-sys-typescale-body-large: 16px;
    --md-sys-typescale-body-medium: 14px;
    --md-sys-typescale-label-large: 14px;
    --md-sys-typescale-label-medium: 12px;

    /* Border radius */
    --md-sys-shape-corner-none: 0px;
    --md-sys-shape-corner-extra-small: 4px;
    --md-sys-shape-corner-small: 8px;
    --md-sys-shape-corner-medium: 12px;
    --md-sys-shape-corner-large: 16px;
    --md-sys-shape-corner-extra-large: 28px;
}

[data-theme="dark"] {
    /* Dark Theme - Material Design 3 */
    --md-sys-color-primary: #90caf9;
    --md-sys-color-on-primary: #0d47a1;
    --md-sys-color-primary-container: #1565c0;
    --md-sys-color-on-primary-container: #e3f2fd;

    --md-sys-color-surface: #121212;
    --md-sys-color-surface-variant: #1e1e1e;
    --md-sys-color-surface-container: #1f1f1f;
    --md-sys-color-surface-container-high: #2c2c2c;
    --md-sys-color-on-surface: #e6e1e5;
    --md-sys-color-on-surface-variant: #cac4d0;

    --md-sys-color-outline: #424242;
    --md-sys-color-outline-variant: #2c2c2c;

    --md-sys-color-success: #81c784;
    --md-sys-color-warning: #ffb74d;
    --md-sys-color-error: #e57373;
    --md-sys-color-info: #64b5f6;

    /* Elevation shadows for dark theme */
    --md-sys-elevation-1: 0px 1px 3px rgba(0, 0, 0, 0.2), 0px 1px 2px rgba(0, 0, 0, 0.14);
    --md-sys-elevation-2: 0px 3px 6px rgba(0, 0, 0, 0.2), 0px 3px 6px rgba(0, 0, 0, 0.14);
    --md-sys-elevation-3: 0px 10px 20px rgba(0, 0, 0, 0.2), 0px 6px 6px rgba(0, 0, 0, 0.14);
    --md-sys-elevation-4: 0px 14px 28px rgba(0, 0, 0, 0.2), 0px 10px 10px rgba(0, 0, 0, 0.14);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface);
    line-height: 1.5;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    font-size: var(--md-sys-typescale-body-medium);
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background-color: var(--md-sys-color-surface);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    box-shadow: var(--md-sys-elevation-1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    max-width: 1400px;
    margin: 0 auto;
}

.header h1 {
    color: var(--md-sys-color-primary);
    font-size: var(--md-sys-typescale-title-large);
    font-weight: 500;
    letter-spacing: 0.15px;
}

.header h1 i {
    margin-right: 8px;
}

.header-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Material Design Button Styles */
.btn {
    padding: 10px 24px;
    border: none;
    border-radius: var(--md-sys-shape-corner-large);
    cursor: pointer;
    font-size: var(--md-sys-typescale-label-large);
    font-weight: 500;
    letter-spacing: 0.1px;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-height: 40px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: currentColor;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.btn:hover::before {
    opacity: 0.08;
}

.btn:focus::before {
    opacity: 0.12;
}

.btn:active::before {
    opacity: 0.16;
}

.btn-primary {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    box-shadow: var(--md-sys-elevation-1);
}

.btn-primary:hover {
    box-shadow: var(--md-sys-elevation-2);
}

.btn-secondary {
    background-color: var(--md-sys-color-surface-container);
    color: var(--md-sys-color-on-surface);
    border: 1px solid var(--md-sys-color-outline);
}

.btn-secondary:hover {
    background-color: var(--md-sys-color-surface-container-high);
}

.btn-sm {
    padding: 6px 16px;
    font-size: var(--md-sys-typescale-label-medium);
    min-height: 32px;
}

.theme-toggle {
    background: var(--md-sys-color-surface-container);
    border: 1px solid var(--md-sys-color-outline);
    color: var(--md-sys-color-on-surface);
    padding: 8px;
    border-radius: var(--md-sys-shape-corner-medium);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background-color: var(--md-sys-color-surface-container-high);
    box-shadow: var(--md-sys-elevation-1);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Filters Section */
.filters-section {
    background-color: var(--md-sys-color-surface);
    padding: 20px;
    border-radius: var(--md-sys-shape-corner-large);
    margin-bottom: 24px;
    box-shadow: var(--md-sys-elevation-1);
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
}

.search-container {
    position: relative;
    flex: 1;
    min-width: 280px;
}

.search-container i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--md-sys-color-on-surface-variant);
    font-size: 18px;
}

.search-container input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid var(--md-sys-color-outline);
    border-radius: var(--md-sys-shape-corner-large);
    background-color: var(--md-sys-color-surface-container);
    color: var(--md-sys-color-on-surface);
    font-size: var(--md-sys-typescale-body-large);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.search-container input:focus {
    outline: none;
    border-color: var(--md-sys-color-primary);
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.12);
    background-color: var(--md-sys-color-surface);
}

.search-container input::placeholder {
    color: var(--md-sys-color-on-surface-variant);
}

.filter-tabs {
    display: flex;
    gap: 4px;
    background-color: var(--md-sys-color-surface-container);
    border-radius: var(--md-sys-shape-corner-large);
    padding: 4px;
}

.filter-tab {
    padding: 8px 16px;
    border: none;
    background-color: transparent;
    color: var(--md-sys-color-on-surface);
    border-radius: var(--md-sys-shape-corner-medium);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    font-size: var(--md-sys-typescale-label-large);
    font-weight: 500;
    position: relative;
}

.filter-tab.active {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    box-shadow: var(--md-sys-elevation-1);
}

.filter-tab:hover:not(.active) {
    background-color: var(--md-sys-color-surface-container-high);
}

.action-buttons {
    display: flex;
    gap: 8px;
}

/* Material Design Table Styles */
.table-container {
    background-color: var(--md-sys-color-surface);
    border-radius: var(--md-sys-shape-corner-large);
    overflow: hidden;
    box-shadow: var(--md-sys-elevation-1);
}

.work-orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--md-sys-typescale-body-medium);
}

.work-orders-table th {
    background-color: var(--md-sys-color-surface-container);
    color: var(--md-sys-color-on-surface);
    padding: 16px 12px;
    text-align: left;
    font-weight: 500;
    font-size: var(--md-sys-typescale-label-large);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    position: sticky;
    top: 0;
    z-index: 10;
    letter-spacing: 0.1px;
}

.work-orders-table td {
    padding: 12px;
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    vertical-align: middle;
    background-color: var(--md-sys-color-surface);
}

.work-orders-table tbody tr {
    transition: background-color 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
}

.work-orders-table tbody tr:hover {
    background-color: var(--md-sys-color-surface-container);
}

.work-orders-table tbody tr.expanded {
    background-color: var(--md-sys-color-surface-container);
}

/* Material Design Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: var(--md-sys-shape-corner-small);
    font-size: var(--md-sys-typescale-label-medium);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    min-height: 24px;
}

.status-badge.in-progress {
    background-color: var(--md-sys-color-info);
    color: white;
}

.status-badge.scheduled {
    background-color: var(--md-sys-color-warning);
    color: #000;
}

.status-badge.drop-in {
    background-color: var(--md-sys-color-success);
    color: white;
}

.status-badge.completed {
    background-color: var(--md-sys-color-success);
    color: white;
}

.status-badge.pending {
    background-color: var(--md-sys-color-warning);
    color: #000;
}

.status-badge.cancelled {
    background-color: var(--md-sys-color-error);
    color: white;
}

.status-badge.moved-to-tech {
    background-color: var(--md-sys-color-info);
    color: white;
}

.status-badge.created {
    background-color: #6c757d;
    color: white;
}

/* Priority Badge Styles */
.priority-badge {
    padding: 4px 8px;
    border-radius: var(--md-sys-shape-corner-small);
    font-size: var(--md-sys-typescale-label-medium);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.priority-critical {
    background-color: var(--md-sys-color-error);
    color: white;
}

.priority-badge.priority-high {
    background-color: var(--md-sys-color-warning);
    color: #000;
}

.priority-badge.priority-medium {
    background-color: var(--md-sys-color-info);
    color: white;
}

.priority-badge.priority-low {
    background-color: var(--md-sys-color-success);
    color: white;
}

/* Expandable Row */
.expandable-icon {
    cursor: pointer;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
}

.expandable-icon.expanded {
    transform: rotate(90deg);
}

.work-order-details {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Material Design Expanded Work Order Header */
.expanded-work-order-header {
    padding: 16px 20px;
    background-color: var(--md-sys-color-surface-container);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Material Design Expanded Work Order Table */
.expanded-work-order-table {
    padding: 0;
    overflow-x: auto;
    background-color: var(--md-sys-color-surface);
}

.expanded-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--md-sys-typescale-body-medium);
    background-color: var(--md-sys-color-surface);
    min-width: 1200px; /* Ensure horizontal scroll for many columns */
}

.expanded-table th {
    background-color: var(--md-sys-color-surface-container-high);
    color: var(--md-sys-color-on-surface);
    padding: 12px 8px;
    text-align: left;
    font-weight: 500;
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    font-size: var(--md-sys-typescale-label-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.expanded-table td {
    padding: 12px 8px;
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    vertical-align: middle;
    background-color: var(--md-sys-color-surface);
    white-space: nowrap;
}

.expanded-table tbody tr:hover {
    background-color: var(--md-sys-color-surface-container);
}

.expanded-table tbody tr:hover td {
    background-color: var(--md-sys-color-surface-container);
}

.expanded-table .work-order-link {
    color: var(--md-sys-color-primary);
    text-decoration: none;
    font-weight: 500;
    margin-left: 8px;
}

.expanded-table .work-order-link:hover {
    text-decoration: underline;
}

.expanded-table i.fas {
    color: var(--md-sys-color-on-surface-variant);
    font-size: 12px;
}

/* Legacy detail styles for backward compatibility */
.work-order-details-content {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.detail-section h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
}

.modal-overlay.active {
    display: flex;
}

.modal-container {
    background-color: var(--bg-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-secondary);
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    background-color: var(--bg-secondary);
}

/* Work Order Info Bar */
.work-order-info-bar {
    background-color: var(--bg-secondary);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
    background-color: var(--bg-primary);
}

.tab-btn:hover:not(.active) {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

/* Tab Content */
.tab-content {
    padding: 2rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group input[readonly] {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Asset Details Specific Styles */
.asset-warranty-section h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.warranty-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.warranty-column h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.asset-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

/* Planning Grid */
.planning-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.planning-section h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

/* Job Details Specific Styles */
.status-badges-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: 8px;
}

.status-badges-container .status-badge {
    font-size: 0.75rem;
}

.status-badge.created {
    background-color: #6c757d;
    color: white;
}

.status-badge.moved-to-tech {
    background-color: var(--info-color);
    color: white;
}

.status-badge.job-completed {
    background-color: var(--success-color);
    color: white;
}

.status-badge.assign-to-parts {
    background-color: #fd7e14;
    color: white;
}

.status-badge.billed {
    background-color: #20c997;
    color: white;
}

.status-badge.closed {
    background-color: #6f42c1;
    color: white;
}

.job-form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-tabs {
        justify-content: center;
    }

    .action-buttons {
        justify-content: center;
    }

    .table-container {
        overflow-x: auto;
    }

    .work-orders-table {
        min-width: 800px;
    }

    .modal-container {
        margin: 0.5rem;
        max-height: 95vh;
    }

    .modal-header,
    .modal-footer {
        padding: 1rem;
    }

    .tab-content {
        padding: 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .warranty-grid,
    .planning-grid {
        grid-template-columns: 1fr;
    }

    .work-order-info-bar {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .tab-navigation {
        overflow-x: auto;
    }

    .tab-btn {
        white-space: nowrap;
        min-width: 150px;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
