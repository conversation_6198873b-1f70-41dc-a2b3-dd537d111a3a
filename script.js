// Global variables
let currentWorkOrders = [...workOrdersData];
let currentFilter = 'all';
let expandedRows = new Set();

// DOM elements
const themeToggle = document.getElementById('themeToggle');
const searchInput = document.getElementById('searchInput');
const filterTabs = document.querySelectorAll('.filter-tab');
const workOrdersTableBody = document.getElementById('workOrdersTableBody');
const workOrderModal = document.getElementById('workOrderModal');
const closeModal = document.getElementById('closeModal');
const cancelBtn = document.getElementById('cancelBtn');
const saveBtn = document.getElementById('saveBtn');
const newWorkOrderBtn = document.getElementById('newWorkOrderBtn');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    renderWorkOrders();
    setupEventListeners();
});

// Theme management
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
}

function updateThemeIcon(theme) {
    const icon = themeToggle.querySelector('i');
    icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

// Event listeners setup
function setupEventListeners() {
    themeToggle.addEventListener('click', toggleTheme);
    searchInput.addEventListener('input', handleSearch);
    closeModal.addEventListener('click', closeWorkOrderModal);
    cancelBtn.addEventListener('click', closeWorkOrderModal);
    saveBtn.addEventListener('click', saveWorkOrder);
    newWorkOrderBtn.addEventListener('click', openNewWorkOrderModal);

    // Filter tabs
    filterTabs.forEach(tab => {
        tab.addEventListener('click', () => handleFilterChange(tab.dataset.filter));
    });

    // Modal tab navigation
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => switchTab(btn.dataset.tab));
    });

    // Close modal when clicking outside
    workOrderModal.addEventListener('click', (e) => {
        if (e.target === workOrderModal) {
            closeWorkOrderModal();
        }
    });

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && workOrderModal.classList.contains('active')) {
            closeWorkOrderModal();
        }
    });
}

// Search functionality
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase().trim();

    if (searchTerm === '') {
        currentWorkOrders = [...workOrdersData];
    } else {
        currentWorkOrders = workOrdersData.filter(wo =>
            wo.workOrderNumber.toLowerCase().includes(searchTerm) ||
            wo.customerName.toLowerCase().includes(searchTerm) ||
            wo.customerAccount.toLowerCase().includes(searchTerm) ||
            wo.serialNumber.toLowerCase().includes(searchTerm) ||
            wo.quotationNumber.toLowerCase().includes(searchTerm) ||
            wo.status.toLowerCase().includes(searchTerm)
        );
    }

    applyCurrentFilter();
    renderWorkOrders();
}

// Filter functionality
function handleFilterChange(filter) {
    currentFilter = filter;

    // Update active tab
    filterTabs.forEach(tab => {
        tab.classList.toggle('active', tab.dataset.filter === filter);
    });

    applyCurrentFilter();
    renderWorkOrders();
}

function applyCurrentFilter() {
    // For demo purposes, we'll just show all orders
    // In a real application, you would filter based on user assignments, etc.
    switch (currentFilter) {
        case 'my':
            // Filter for current user's work orders
            break;
        case 'group':
            // Filter for group work orders
            break;
        default:
            // Show all work orders
            break;
    }
}

// Render work orders table
function renderWorkOrders() {
    workOrdersTableBody.innerHTML = '';

    currentWorkOrders.forEach(workOrder => {
        const row = createWorkOrderRow(workOrder);
        workOrdersTableBody.appendChild(row);

        // Add expanded details if row was previously expanded
        if (expandedRows.has(workOrder.id)) {
            const detailsRow = createWorkOrderDetailsRow(workOrder);
            workOrdersTableBody.appendChild(detailsRow);
        }
    });
}

// Create work order table row
function createWorkOrderRow(workOrder) {
    const row = document.createElement('tr');
    row.className = expandedRows.has(workOrder.id) ? 'expanded' : '';
    row.dataset.workOrderId = workOrder.id;
    row.style.cursor = 'pointer';

    const statusClass = statusColors[workOrder.status] || 'default';

    row.innerHTML = `
        <td>
            <i class="fas fa-chevron-right expandable-icon ${expandedRows.has(workOrder.id) ? 'expanded' : ''}"
               onclick="event.stopPropagation(); toggleRowExpansion(${workOrder.id})"></i>
        </td>
        <td>${workOrder.workOrderNumber}</td>
        <td>${workOrder.customerAccount}</td>
        <td>${workOrder.customerName}</td>
        <td>${workOrder.serialNumber}</td>
        <td>${workOrder.quotationNumber}</td>
        <td>${workOrder.ticketNumber}</td>
        <td>${formatDate(workOrder.plannedStartDate)}</td>
        <td>${formatDate(workOrder.plannedCompletionDate)}</td>
        <td>${workOrder.unitNumber}</td>
        <td><span class="status-badge ${statusClass}">${workOrder.status}</span></td>
        <td>${workOrder.keyTag}</td>
        <td>${workOrder.bay}</td>
    `;

    // Add click event to the entire row (except the expand icon)
    row.addEventListener('click', () => toggleRowExpansion(workOrder.id));

    return row;
}

// Create expanded work order details row
function createWorkOrderDetailsRow(workOrder) {
    const detailsRow = document.createElement('tr');
    detailsRow.className = 'work-order-details';

    const statusClass = statusColors[workOrder.status] || 'default';
    const quotationStatusClass = statusColors[workOrder.quotationStatus] || 'default';

    detailsRow.innerHTML = `
        <td colspan="13">
            <div class="expanded-work-order-header">
                <div class="header-actions">
                    <button class="btn btn-secondary btn-sm">Pull Back</button>
                    <button class="btn btn-secondary btn-sm">Addition Job/Estimate</button>
                    <button class="btn btn-secondary btn-sm">View</button>
                </div>
            </div>
            <table class="expanded-details-table">
                <tbody>
                    <!-- Row 1: Work Order # and Basic Info -->
                    <tr class="details-row">
                        <td class="field-label">Work Order #</td>
                        <td class="field-value work-order-cell">
                            <i class="fas fa-external-link-alt"></i>
                            <a href="#" onclick="openWorkOrderModal(${workOrder.id})" class="work-order-link">
                                ${workOrder.workOrderNumber}
                            </a>
                        </td>
                        <td class="field-label">Customer Complaint</td>
                        <td class="field-value" title="${safeGet(workOrder, 'jobDetails.customerComplaint')}">${safeGet(workOrder, 'jobDetails.customerComplaint') || 'No complaint specified'}</td>
                        <td class="field-label">Status</td>
                        <td class="field-value"><span class="status-badge ${statusClass}">${workOrder.status}</span></td>
                    </tr>

                    <!-- Row 2: Quotation and Assignment Info -->
                    <tr class="details-row">
                        <td class="field-label">Quotation Status</td>
                        <td class="field-value"><span class="status-badge ${quotationStatusClass}">${workOrder.quotationStatus || 'Approved'}</span></td>
                        <td class="field-label">Assigned To</td>
                        <td class="field-value">${workOrder.assignedTo || 'Admin'}</td>
                        <td class="field-label">Customer Account #</td>
                        <td class="field-value">${workOrder.customerAccount}</td>
                    </tr>

                    <!-- Row 3: Customer and Serial Info -->
                    <tr class="details-row">
                        <td class="field-label">Customer Name</td>
                        <td class="field-value">${workOrder.customerName}</td>
                        <td class="field-label">Serial #</td>
                        <td class="field-value">${workOrder.serialNumber}</td>
                        <td class="field-label">Quotation #</td>
                        <td class="field-value">${workOrder.quotationNumber}</td>
                    </tr>

                    <!-- Row 4: Financial and Date Info -->
                    <tr class="details-row">
                        <td class="field-label">Fin. Year</td>
                        <td class="field-value">${workOrder.finYear || '2024-25'}</td>
                        <td class="field-label">Date</td>
                        <td class="field-value">${formatDate(workOrder.date || workOrder.workOrderDate)}</td>
                        <td class="field-label">Model</td>
                        <td class="field-value">${workOrder.model || '-'}</td>
                    </tr>

                    <!-- Row 5: Asset and Service Details -->
                    <tr class="details-row">
                        <td class="field-label">Unit #</td>
                        <td class="field-value">${workOrder.unitNumber || '-'}</td>
                        <td class="field-label">Ticket #</td>
                        <td class="field-value">${workOrder.ticketNumber || '-'}</td>
                        <td class="field-label">Service Type</td>
                        <td class="field-value">${workOrder.serviceType || 'General'}</td>
                    </tr>

                    <!-- Row 6: Priority and Rate Info -->
                    <tr class="details-row">
                        <td class="field-label">Work Priority</td>
                        <td class="field-value"><span class="priority-badge priority-${(workOrder.workPriority || 'medium').toLowerCase()}">${workOrder.workPriority || 'Medium'}</span></td>
                        <td class="field-label">Is Mobile Rate?</td>
                        <td class="field-value"><span class="mobile-rate-indicator ${workOrder.isMobileRate ? 'yes' : 'no'}">${workOrder.isMobileRate ? 'Yes' : 'No'}</span></td>
                        <td class="field-label"></td>
                        <td class="field-value"></td>
                    </tr>
                </tbody>
            </table>
        </td>
    `;

    return detailsRow;
}

// Toggle row expansion
function toggleRowExpansion(workOrderId) {
    const icon = document.querySelector(`tr[data-work-order-id="${workOrderId}"] .expandable-icon`);
    const row = document.querySelector(`tr[data-work-order-id="${workOrderId}"]`);

    if (expandedRows.has(workOrderId)) {
        expandedRows.delete(workOrderId);
        icon.classList.remove('expanded');
        row.classList.remove('expanded');

        // Remove details row
        const detailsRow = row.nextElementSibling;
        if (detailsRow && detailsRow.classList.contains('work-order-details')) {
            detailsRow.remove();
        }
    } else {
        expandedRows.add(workOrderId);
        icon.classList.add('expanded');
        row.classList.add('expanded');

        // Add details row
        const workOrder = workOrdersData.find(wo => wo.id === workOrderId);
        const detailsRow = createWorkOrderDetailsRow(workOrder);
        row.parentNode.insertBefore(detailsRow, row.nextSibling);
    }
}

// Modal functionality
function openWorkOrderModal(workOrderId) {
    const workOrder = workOrdersData.find(wo => wo.id === workOrderId);
    if (!workOrder) return;

    populateModalData(workOrder);
    workOrderModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function openNewWorkOrderModal() {
    // For new work order, clear all fields
    clearModalData();
    document.getElementById('modalTitle').textContent = 'New Work Order';
    workOrderModal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeWorkOrderModal() {
    workOrderModal.classList.remove('active');
    document.body.style.overflow = '';
}

function populateModalData(workOrder) {
    // Work order info bar
    document.getElementById('modalWorkOrderNumber').textContent = workOrder.workOrderNumber;
    document.getElementById('modalWorkOrderStatus').textContent = workOrder.status;
    document.getElementById('modalWorkOrderStatus').className = `status-badge ${statusColors[workOrder.status] || 'default'}`;
    document.getElementById('modalWorkOrderDate').textContent = formatDate(workOrder.workOrderDate);
    document.getElementById('modalQuotationNumber').textContent = workOrder.quotationNumber;
    document.getElementById('modalTicketNumber').textContent = workOrder.ticketNumber;
    document.getElementById('modalQuotationDate').textContent = formatDate(workOrder.quotationDate);

    // Customer details
    document.getElementById('customerAccount').value = workOrder.customerAccount;
    document.getElementById('customerName').value = workOrder.customerName;
    document.getElementById('customerMobile').value = workOrder.customerDetails.mobile;
    document.getElementById('customerEmail').value = workOrder.customerDetails.email;
    document.getElementById('customerAddress').value = workOrder.customerDetails.address;
    document.getElementById('poNumber').value = workOrder.customerDetails.poNumber;
    document.getElementById('shipTo').value = workOrder.customerDetails.shipTo;

    // Asset details
    document.getElementById('assetSerial').value = workOrder.serialNumber;
    document.getElementById('assetBrand').value = workOrder.assetDetails.brand;
    document.getElementById('assetBrand2').value = workOrder.assetDetails.brand2;
    document.getElementById('serialStat').value = workOrder.assetDetails.serialStat;
    document.getElementById('assetModel').value = workOrder.assetDetails.model;
    document.getElementById('assetType').value = workOrder.assetDetails.assetType;
    document.getElementById('odometer').value = workOrder.assetDetails.odometer;
    document.getElementById('keyTag').value = workOrder.keyTag;
    document.getElementById('isBreakdown').checked = workOrder.assetDetails.isBreakdown;

    // Planning details
    document.getElementById('expectedArrival').value = workOrder.planningDetails.expectedArrival;
    document.getElementById('actualArrival').value = workOrder.planningDetails.actualArrival;
    document.getElementById('expectedDelivery').value = workOrder.planningDetails.expectedDelivery;
    document.getElementById('actualDelivery').value = workOrder.planningDetails.actualDelivery;
    document.getElementById('plannedStartDate').value = workOrder.plannedStartDate;
    document.getElementById('plannedEndDate').value = workOrder.plannedCompletionDate;
    document.getElementById('actualStartDate').value = workOrder.planningDetails.actualStartDate;
    document.getElementById('actualEndDate').value = workOrder.planningDetails.actualEndDate;

    // Job details
    document.getElementById('customerComplaint').value = workOrder.jobDetails.customerComplaint;
    document.getElementById('causeOfFailure').value = workOrder.jobDetails.causeOfFailure;
    document.getElementById('correctiveAction').value = workOrder.jobDetails.correctiveAction;
    document.getElementById('actionForNextService').value = workOrder.jobDetails.actionForNextService;
    document.getElementById('jobCardStatus').value = workOrder.jobDetails.jobCardStatus;
}

function clearModalData() {
    // Clear all form fields
    const inputs = workOrderModal.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            input.checked = false;
        } else {
            input.value = '';
        }
    });

    // Reset info bar
    document.getElementById('modalWorkOrderNumber').textContent = 'New';
    document.getElementById('modalWorkOrderStatus').textContent = 'Draft';
    document.getElementById('modalWorkOrderStatus').className = 'status-badge created';
    document.getElementById('modalWorkOrderDate').textContent = formatDate(new Date().toISOString().split('T')[0]);
    document.getElementById('modalQuotationNumber').textContent = '';
    document.getElementById('modalTicketNumber').textContent = '';
    document.getElementById('modalQuotationDate').textContent = '';
}

// Tab switching
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Update tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.toggle('active', pane.id === tabName + 'Tab');
    });
}

// Save work order
function saveWorkOrder() {
    // In a real application, this would send data to the server
    console.log('Saving work order...');

    // Show success message (you could implement a toast notification)
    alert('Work order saved successfully!');

    closeWorkOrderModal();
}

// Utility functions
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

function truncateText(text, maxLength = 20) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

function safeGet(obj, path, defaultValue = '') {
    return path.split('.').reduce((current, key) => current && current[key] !== undefined ? current[key] : defaultValue, obj);
}

// Add CSS for work order links
const style = document.createElement('style');
style.textContent = `
    .work-order-link {
        color: var(--accent-color);
        text-decoration: none;
        font-weight: 500;
    }

    .work-order-link:hover {
        text-decoration: underline;
    }
`;
document.head.appendChild(style);
